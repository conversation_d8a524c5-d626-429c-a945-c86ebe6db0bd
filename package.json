{"name": "convai-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@11labs/react": "^0.0.8", "@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "elevenlabs": "^1.54.0", "express": "^4.21.1", "framer-motion": "^11.11.11", "lucide-react": "^0.454.0", "next": "15.0.2", "react": "19.0.0-rc-02c0e824-20241028", "react-dom": "19.0.0-rc-02c0e824-20241028", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}