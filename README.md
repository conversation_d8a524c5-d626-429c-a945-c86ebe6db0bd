## Conversational AI Demo

## Setup

Set up the environment variables:

```bash
cp .env.example .env
```

Follow [this guide](https://elevenlabs.io/docs/conversational-ai/docs/agent-setup) to configure your agent and get your API key and set them in the `.env` file.

Run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

## Learn More

- [Conversational AI Quickstart](https://elevenlabs.io/docs/conversational-ai/quickstart)
- [Conversational AI SDK](https://elevenlabs.io/docs/libraries/conversational-ai-sdk-js)
